import '@testing-library/jest-dom'
import React from 'react'

// Mock Next.js router
jest.mock('next/navigation', () => ({
  useRouter() {
    return {
      push: jest.fn(),
      replace: jest.fn(),
      prefetch: jest.fn(),
      back: jest.fn(),
      forward: jest.fn(),
      refresh: jest.fn(),
    }
  },
  usePathname() {
    return '/'
  },
  useSearchParams() {
    return new URLSearchParams()
  },
}))

// Mock Next.js image component
jest.mock('next/image', () => ({
  __esModule: true,
  default: (props) => {
    // eslint-disable-next-line @next/next/no-img-element
    return <img {...props} />
  },
}))

// Mock framer-motion
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }) => <div {...props}>{children}</div>,
    section: ({ children, ...props }) => <section {...props}>{children}</section>,
    h1: ({ children, ...props }) => <h1 {...props}>{children}</h1>,
    h2: ({ children, ...props }) => <h2 {...props}>{children}</h2>,
    p: ({ children, ...props }) => <p {...props}>{children}</p>,
    button: ({ children, ...props }) => <button {...props}>{children}</button>,
  },
  AnimatePresence: ({ children }) => children,
  useAnimation: () => ({
    start: jest.fn(),
    stop: jest.fn(),
    set: jest.fn(),
  }),
}))

// Mock IntersectionObserver
global.IntersectionObserver = class IntersectionObserver {
  constructor() {}
  observe() {
    return null
  }
  disconnect() {
    return null
  }
  unobserve() {
    return null
  }
}

// Mock ResizeObserver
global.ResizeObserver = class ResizeObserver {
  constructor() {}
  observe() {
    return null
  }
  disconnect() {
    return null
  }
  unobserve() {
    return null
  }
}

// Mock matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(), // deprecated
    removeListener: jest.fn(), // deprecated
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
})

// Mock scrollTo
Object.defineProperty(window, 'scrollTo', {
  writable: true,
  value: jest.fn(),
})

// Mock fetch
global.fetch = jest.fn()

// Setup environment variables for tests
process.env.NEXT_PUBLIC_API_BASE = 'http://localhost:4000'
process.env.NODE_ENV = 'test'

// Mock framer-motion for tests
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, initial, animate, transition, whileHover, whileTap, exit, ...props }) =>
      React.createElement('div', props, children),
    section: ({ children, initial, animate, transition, ...props }) =>
      React.createElement('section', props, children),
    h3: ({ children, initial, animate, transition, ...props }) =>
      React.createElement('h3', props, children),
    a: ({ children, initial, animate, transition, whileHover, whileTap, ...props }) =>
      React.createElement('a', props, children),
  },
  AnimatePresence: ({ children }) => children,
}))

// Mock next-themes for tests
const mockSetTheme = jest.fn()
jest.mock('next-themes', () => ({
  ThemeProvider: ({ children }) => children,
  useTheme: () => ({
    theme: 'light',
    setTheme: mockSetTheme,
    resolvedTheme: 'light',
  }),
}))

// Export the mock for tests to access
global.mockSetTheme = mockSetTheme

// Mock Radix UI Slot for tests
jest.mock('@radix-ui/react-slot', () => ({
  Slot: ({ children, ...props }) => React.createElement('div', props, children),
}))

// Mock Radix UI Tabs for tests
jest.mock('@radix-ui/react-tabs', () => {
  const { useState } = React

  const Root = ({ children, defaultValue, ...props }) => {
    const [activeTab, setActiveTab] = useState(defaultValue)

    return React.createElement('div', {
      ...props,
      'data-testid': 'tabs-root',
      'data-active-tab': activeTab
    }, React.Children.map(children, child =>
      React.cloneElement(child, { activeTab, setActiveTab })
    ))
  }

  const List = ({ children, activeTab, setActiveTab, ...props }) => {
    const { activeTab: _, setActiveTab: __, ...cleanProps } = props
    return React.createElement('div', { ...cleanProps, role: 'tablist' },
      React.Children.map(children, child =>
        React.cloneElement(child, { activeTab, setActiveTab })
      )
    )
  }

  const Trigger = ({ children, value, activeTab, setActiveTab, ...props }) => {
    const { activeTab: _, setActiveTab: __, ...cleanProps } = props
    return React.createElement('button', {
      ...cleanProps,
      role: 'tab',
      'aria-selected': activeTab === value,
      'data-state': activeTab === value ? 'active' : 'inactive',
      onClick: () => setActiveTab(value)
    }, children)
  }

  const Content = ({ children, value, activeTab, ...props }) => {
    const { activeTab: _, setActiveTab: __, ...cleanProps } = props
    return activeTab === value ? React.createElement('div', {
      ...cleanProps,
      role: 'tabpanel',
      'data-state': 'active'
    }, children) : null
  }

  return {
    Root,
    List,
    Trigger,
    Content
  }
})

// Console error suppression for known issues
const originalError = console.error
beforeAll(() => {
  console.error = (...args) => {
    if (
      typeof args[0] === 'string' &&
      args[0].includes('Warning: ReactDOM.render is no longer supported')
    ) {
      return
    }
    originalError.call(console, ...args)
  }
})

afterAll(() => {
  console.error = originalError
})
