import { render, screen, waitFor } from '../utils/test-utils'
import { createMockFetch, createMockFetchError, mockEvents } from '../utils/test-utils'
import EventsPage from '@/app/events/page'

describe('Events Page Integration', () => {
  beforeEach(() => {
    // Reset fetch mock
    global.fetch = jest.fn()
    // Ensure environment variable is set
    process.env.NEXT_PUBLIC_API_BASE = 'http://localhost:4000'

    // Mock Date to be before our test events (February 2025)
    jest.useFakeTimers()
    jest.setSystemTime(new Date('2025-02-01'))
  })

  afterEach(() => {
    jest.restoreAllMocks()
    jest.useRealTimers()
  })

  it('renders events page with loading state initially', () => {
    global.fetch = createMockFetch(mockEvents)

    render(<EventsPage />)

    // Should show the page title
    expect(screen.getByRole('heading', { level: 1, name: /^events$/i })).toBeInTheDocument()
  })

  it('debug: check basic page rendering', async () => {
    const mockFetch = createMockFetch([]) // Empty events to avoid EventCard rendering
    global.fetch = mockFetch

    render(<EventsPage />)

    // Check if basic page elements render
    expect(screen.getByRole('heading', { level: 1, name: /^events$/i })).toBeInTheDocument()
    expect(screen.getByRole('tab', { name: /upcoming events/i })).toBeInTheDocument()
    expect(screen.getByRole('tab', { name: /past events/i })).toBeInTheDocument()
  })

  it('debug: check past events tab', async () => {
    const mockFetch = createMockFetch(mockEvents)
    global.fetch = mockFetch

    render(<EventsPage />)

    // Wait for events to load
    await waitFor(() => {
      expect(screen.getByText('React Workshop')).toBeInTheDocument()
    })

    // Check that past events tab exists
    const pastTab = screen.getByRole('tab', { name: /past events/i })
    expect(pastTab).toBeInTheDocument()

    // Click on past events tab
    pastTab.click()

    // Wait for tab content to update
    await waitFor(() => {
      const tabPanel = screen.getByRole('tabpanel')
      console.log('Tab panel content:', tabPanel.textContent)
      // Check if we can find the networking event or the fallback message
      const hasNetworkingEvent = screen.queryByText('Networking Event')
      const hasFallbackMessage = screen.queryByText('No past events to display.')
      console.log('Has Networking Event:', !!hasNetworkingEvent)
      console.log('Has fallback message:', !!hasFallbackMessage)

      // At least one should be present
      expect(hasNetworkingEvent || hasFallbackMessage).toBeTruthy()
    })
  })

  it('displays events after successful API call', async () => {
    global.fetch = createMockFetch(mockEvents)

    render(<EventsPage />)

    // Wait for events to load
    await waitFor(() => {
      expect(screen.getByText('React Workshop')).toBeInTheDocument()
    }, { timeout: 5000 })

    expect(screen.getByText('Python Bootcamp')).toBeInTheDocument()
    expect(screen.getByText('Learn React fundamentals and advanced concepts')).toBeInTheDocument()
  })

  it('separates upcoming and past events correctly', async () => {
    global.fetch = createMockFetch(mockEvents)

    render(<EventsPage />)

    await waitFor(() => {
      expect(screen.getByText('React Workshop')).toBeInTheDocument()
    })

    // Check that upcoming events tab is available
    expect(screen.getByRole('tab', { name: /upcoming events/i })).toBeInTheDocument()
    expect(screen.getByRole('tab', { name: /past events/i })).toBeInTheDocument()

    // Check that upcoming events are visible by default
    expect(screen.getByText('React Workshop')).toBeInTheDocument()
    expect(screen.getByText('Python Bootcamp')).toBeInTheDocument()

    // Click on past events tab to see past events
    const pastTab = screen.getByRole('tab', { name: /past events/i })
    pastTab.click()

    await waitFor(() => {
      expect(screen.getByText('Networking Event')).toBeInTheDocument()
    })
  })

  it('handles API errors gracefully', async () => {
    global.fetch = createMockFetchError('Network error')
    
    render(<EventsPage />)

    // Should show error message
    await waitFor(() => {
      expect(screen.getByText(/error loading events/i)).toBeInTheDocument()
    })

    // Should show retry button
    expect(screen.getByRole('button', { name: /try again/i })).toBeInTheDocument()
  })

  it('allows expanding event details', async () => {
    global.fetch = createMockFetch(mockEvents)

    render(<EventsPage />)

    await waitFor(() => {
      expect(screen.getByText('React Workshop')).toBeInTheDocument()
    })

    // Find and click the expand button for React Workshop
    const expandButtons = screen.getAllByRole('button', { name: /more info/i })
    const reactWorkshopButton = expandButtons[0]

    // Click to expand
    reactWorkshopButton.click()

    // Should show expanded content
    await waitFor(() => {
      expect(screen.getByText(/event details/i)).toBeInTheDocument()
    })

    expect(screen.getByText(/basic javascript knowledge/i)).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /register now/i })).toBeInTheDocument()
  })

  it('shows registration button for upcoming events', async () => {
    global.fetch = createMockFetch(mockEvents)

    render(<EventsPage />)

    await waitFor(() => {
      expect(screen.getByText('React Workshop')).toBeInTheDocument()
    })

    // Expand the first event
    const expandButtons = screen.getAllByRole('button', { name: /more info/i })
    expandButtons[0].click()

    await waitFor(() => {
      expect(screen.getByRole('button', { name: /register now/i })).toBeInTheDocument()
    })

    const registerButton = screen.getByRole('button', { name: /register now/i })
    expect(registerButton.closest('a')).toHaveAttribute('href', mockEvents[0].registrationLink)
  })

  it('shows closed registration for past events', async () => {
    global.fetch = createMockFetch(mockEvents)

    render(<EventsPage />)

    // Click on past events tab
    const pastTab = screen.getByRole('tab', { name: /past events/i })
    pastTab.click()

    await waitFor(() => {
      expect(screen.getByText('Networking Event')).toBeInTheDocument()
    })

    // Find and expand the past event
    const expandButtons = screen.getAllByRole('button', { name: /more info/i })
    if (expandButtons.length > 0) {
      expandButtons[0].click()

      await waitFor(() => {
        expect(screen.getByRole('button', { name: /event completed/i })).toBeInTheDocument()
      })

      const closedButton = screen.getByRole('button', { name: /event completed/i })
      expect(closedButton).toBeDisabled()
    }
  })

  it('handles empty events list', async () => {
    global.fetch = createMockFetch([])
    
    render(<EventsPage />)

    await waitFor(() => {
      expect(screen.getByText(/no upcoming events at the moment/i)).toBeInTheDocument()
    })
  })

  it('displays multiple upcoming events', async () => {
    const multipleUpcomingEvents = mockEvents.filter(event =>
      new Date(event.date) > new Date()
    )

    global.fetch = createMockFetch(multipleUpcomingEvents)

    render(<EventsPage />)

    await waitFor(() => {
      expect(screen.getByText('React Workshop')).toBeInTheDocument()
    })

    // Should show multiple events in grid layout
    if (multipleUpcomingEvents.length > 1) {
      expect(screen.getByText('React Workshop')).toBeInTheDocument()
      expect(screen.getByText('Python Bootcamp')).toBeInTheDocument()
    }
  })

  it('has proper accessibility structure', async () => {
    global.fetch = createMockFetch(mockEvents)

    render(<EventsPage />)

    await waitFor(() => {
      expect(screen.getByText('React Workshop')).toBeInTheDocument()
    })

    // Check for proper heading hierarchy
    expect(screen.getByRole('heading', { level: 1, name: /events/i })).toBeInTheDocument()
    expect(screen.getByRole('heading', { level: 2, name: /why attend our events/i })).toBeInTheDocument()

    // Check for proper landmark regions
    const main = screen.getByRole('main')
    expect(main).toBeInTheDocument()

    // Check for proper tab structure
    expect(screen.getByRole('tab', { name: /upcoming events/i })).toBeInTheDocument()
    expect(screen.getByRole('tab', { name: /past events/i })).toBeInTheDocument()

    // Check for proper button labels
    const expandButtons = screen.getAllByRole('button')
    expandButtons.forEach(button => {
      expect(button).toHaveAccessibleName()
    })
  })

  it('retries API call when retry button is clicked', async () => {
    // First call fails
    global.fetch = createMockFetchError('Network error')
    
    render(<EventsPage />)

    await waitFor(() => {
      expect(screen.getByText(/error loading events/i)).toBeInTheDocument()
    })

    // Mock successful retry
    global.fetch = createMockFetch(mockEvents)

    // Click retry
    const retryButton = screen.getByRole('button', { name: /try again/i })
    retryButton.click()

    // Should show events after retry
    await waitFor(() => {
      expect(screen.getByText('React Workshop')).toBeInTheDocument()
    })
  })
})
