import { render, screen, fireEvent, waitFor, checkAccessibility } from '../utils/test-utils'
import userEvent from '@testing-library/user-event'
import { Navbar } from '@/components/navbar'

// Use the global mock from jest.setup.js

describe('Navbar', () => {
  beforeEach(() => {
    // Reset any mocks
    jest.clearAllMocks()
  })

  it('renders the navbar with logo and navigation links', () => {
    render(<Navbar />)

    // Check logo
    expect(screen.getByAltText('GeeksforGeeks Student Chapter')).toBeInTheDocument()

    // Check desktop navigation links
    expect(screen.getByRole('link', { name: /events/i })).toBeInTheDocument()
    expect(screen.getByRole('link', { name: /about/i })).toBeInTheDocument()
    expect(screen.getByRole('link', { name: /learning/i })).toBeInTheDocument()

    // Check POTD link (desktop version)
    expect(screen.getByRole('link', { name: /geeksforgeeks problem of the day/i })).toBeInTheDocument()

    // Check mobile menu button
    expect(screen.getByRole('button', { name: /open menu/i })).toBeInTheDocument()
  })

  it('has proper accessibility attributes', () => {
    render(<Navbar />)

    // Check main navigation landmark
    const nav = screen.getByRole('navigation', { name: /main navigation/i })
    expect(nav).toBeInTheDocument()
    expect(nav).toHaveAttribute('id', 'main-navigation')

    // Check that navigation has proper aria-label
    expect(nav).toHaveAttribute('aria-label', 'Main navigation')
  })

  it('toggles mobile menu when hamburger button is clicked', async () => {
    const user = userEvent.setup()
    render(<Navbar />)
    
    // Find mobile menu button
    const menuButton = screen.getByRole('button', { name: /open menu/i })
    expect(menuButton).toBeInTheDocument()
    
    // Initially menu should be closed
    expect(screen.getByRole('button', { name: /open menu/i })).toBeInTheDocument()
    
    // Click to open menu
    await user.click(menuButton)
    
    // Menu should now be open
    await waitFor(() => {
      expect(screen.getByRole('button', { name: /close menu/i })).toBeInTheDocument()
    })
    
    // Mobile menu should be visible
    const mobileMenu = screen.getByRole('menu')
    expect(mobileMenu).toBeInTheDocument()
  })

  it('closes mobile menu when a navigation link is clicked', async () => {
    const user = userEvent.setup()
    render(<Navbar />)
    
    // Open mobile menu
    const menuButton = screen.getByRole('button', { name: /open menu/i })
    await user.click(menuButton)
    
    // Wait for menu to open
    await waitFor(() => {
      expect(screen.getByRole('button', { name: /close menu/i })).toBeInTheDocument()
    })
    
    // Click on a navigation link in mobile menu
    const eventsLink = screen.getAllByRole('menuitem', { name: /events/i })[0]
    await user.click(eventsLink)
    
    // Menu should close (button text changes back)
    await waitFor(() => {
      expect(screen.getByRole('button', { name: /open menu/i })).toBeInTheDocument()
    })
  })

  it('has proper keyboard navigation support', async () => {
    const user = userEvent.setup()
    render(<Navbar />)

    // Tab to the first focusable element (logo link)
    await user.tab()
    expect(screen.getByRole('link', { name: /geeksforgeeks student chapter - home/i })).toHaveFocus()

    // Tab to navigation links
    await user.tab()
    expect(screen.getByRole('link', { name: /events/i })).toHaveFocus()

    await user.tab()
    expect(screen.getByRole('link', { name: /about/i })).toHaveFocus()
  })

  it('indicates current page in navigation', () => {
    render(<Navbar />)

    // Check that navigation links have proper href attributes
    const eventsLink = screen.getByRole('link', { name: /events/i })
    expect(eventsLink).toHaveAttribute('href', '/events')

    const aboutLink = screen.getByRole('link', { name: /about/i })
    expect(aboutLink).toHaveAttribute('href', '/about')

    const learningLink = screen.getByRole('link', { name: /learning/i })
    expect(learningLink).toHaveAttribute('href', '/learning')
  })

  it('has proper ARIA attributes for mobile menu', async () => {
    const user = userEvent.setup()
    render(<Navbar />)
    
    const menuButton = screen.getByRole('button', { name: /open menu/i })
    
    // Check initial ARIA attributes
    expect(menuButton).toHaveAttribute('aria-expanded', 'false')
    expect(menuButton).toHaveAttribute('aria-controls', 'mobile-menu')
    
    // Open menu
    await user.click(menuButton)
    
    // Check updated ARIA attributes
    await waitFor(() => {
      expect(screen.getByRole('button', { name: /close menu/i })).toHaveAttribute('aria-expanded', 'true')
    })
    
    // Check mobile menu attributes
    const mobileMenu = screen.getByRole('menu')
    expect(mobileMenu).toHaveAttribute('id', 'mobile-menu')
  })

  it('handles theme toggle', () => {
    render(<Navbar />)

    // Find theme toggle button
    const themeButton = screen.getByRole('button', { name: /toggle theme/i })
    expect(themeButton).toBeInTheDocument()

    // Clear any previous calls to the mock before clicking
    global.mockSetTheme.mockClear()

    // Click theme toggle using fireEvent
    fireEvent.click(themeButton)

    // setTheme should be called with 'dark' (since current theme is 'light')
    expect(global.mockSetTheme).toHaveBeenCalledWith('dark')
  })

  it('has proper external link attributes', () => {
    render(<Navbar />)
    
    const potdLink = screen.getByRole('link', { name: /GeeksforGeeks Problem of the Day.*opens in new tab/i })
    expect(potdLink).toHaveAttribute('target', '_blank')
    expect(potdLink).toHaveAttribute('rel', 'noopener noreferrer')
  })

  it('supports keyboard navigation with Enter and Space keys', async () => {
    const user = userEvent.setup()
    render(<Navbar />)
    
    const menuButton = screen.getByRole('button', { name: /open menu/i })
    
    // Focus the menu button
    menuButton.focus()
    expect(menuButton).toHaveFocus()
    
    // Press Enter to open menu
    await user.keyboard('{Enter}')
    
    await waitFor(() => {
      expect(screen.getByRole('button', { name: /close menu/i })).toBeInTheDocument()
    })
  })

  it('meets accessibility standards', async () => {
    const { container } = render(<Navbar />)
    await checkAccessibility(container)
  })
})
