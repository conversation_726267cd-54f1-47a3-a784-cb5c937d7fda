
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for app/page.tsx</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../prettify.css" />
    <link rel="stylesheet" href="../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../index.html">All files</a> / <a href="index.html">app</a> page.tsx</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>0/29</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>0/4</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>0/8</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>0/28</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a>
<a name='L59'></a><a href='#L59'>59</a>
<a name='L60'></a><a href='#L60'>60</a>
<a name='L61'></a><a href='#L61'>61</a>
<a name='L62'></a><a href='#L62'>62</a>
<a name='L63'></a><a href='#L63'>63</a>
<a name='L64'></a><a href='#L64'>64</a>
<a name='L65'></a><a href='#L65'>65</a>
<a name='L66'></a><a href='#L66'>66</a>
<a name='L67'></a><a href='#L67'>67</a>
<a name='L68'></a><a href='#L68'>68</a>
<a name='L69'></a><a href='#L69'>69</a>
<a name='L70'></a><a href='#L70'>70</a>
<a name='L71'></a><a href='#L71'>71</a>
<a name='L72'></a><a href='#L72'>72</a>
<a name='L73'></a><a href='#L73'>73</a>
<a name='L74'></a><a href='#L74'>74</a>
<a name='L75'></a><a href='#L75'>75</a>
<a name='L76'></a><a href='#L76'>76</a>
<a name='L77'></a><a href='#L77'>77</a>
<a name='L78'></a><a href='#L78'>78</a>
<a name='L79'></a><a href='#L79'>79</a>
<a name='L80'></a><a href='#L80'>80</a>
<a name='L81'></a><a href='#L81'>81</a>
<a name='L82'></a><a href='#L82'>82</a>
<a name='L83'></a><a href='#L83'>83</a>
<a name='L84'></a><a href='#L84'>84</a>
<a name='L85'></a><a href='#L85'>85</a>
<a name='L86'></a><a href='#L86'>86</a>
<a name='L87'></a><a href='#L87'>87</a>
<a name='L88'></a><a href='#L88'>88</a>
<a name='L89'></a><a href='#L89'>89</a>
<a name='L90'></a><a href='#L90'>90</a>
<a name='L91'></a><a href='#L91'>91</a>
<a name='L92'></a><a href='#L92'>92</a>
<a name='L93'></a><a href='#L93'>93</a>
<a name='L94'></a><a href='#L94'>94</a>
<a name='L95'></a><a href='#L95'>95</a>
<a name='L96'></a><a href='#L96'>96</a>
<a name='L97'></a><a href='#L97'>97</a>
<a name='L98'></a><a href='#L98'>98</a>
<a name='L99'></a><a href='#L99'>99</a>
<a name='L100'></a><a href='#L100'>100</a>
<a name='L101'></a><a href='#L101'>101</a>
<a name='L102'></a><a href='#L102'>102</a>
<a name='L103'></a><a href='#L103'>103</a>
<a name='L104'></a><a href='#L104'>104</a>
<a name='L105'></a><a href='#L105'>105</a>
<a name='L106'></a><a href='#L106'>106</a>
<a name='L107'></a><a href='#L107'>107</a>
<a name='L108'></a><a href='#L108'>108</a>
<a name='L109'></a><a href='#L109'>109</a>
<a name='L110'></a><a href='#L110'>110</a>
<a name='L111'></a><a href='#L111'>111</a>
<a name='L112'></a><a href='#L112'>112</a>
<a name='L113'></a><a href='#L113'>113</a>
<a name='L114'></a><a href='#L114'>114</a>
<a name='L115'></a><a href='#L115'>115</a>
<a name='L116'></a><a href='#L116'>116</a>
<a name='L117'></a><a href='#L117'>117</a>
<a name='L118'></a><a href='#L118'>118</a>
<a name='L119'></a><a href='#L119'>119</a>
<a name='L120'></a><a href='#L120'>120</a>
<a name='L121'></a><a href='#L121'>121</a>
<a name='L122'></a><a href='#L122'>122</a>
<a name='L123'></a><a href='#L123'>123</a>
<a name='L124'></a><a href='#L124'>124</a>
<a name='L125'></a><a href='#L125'>125</a>
<a name='L126'></a><a href='#L126'>126</a>
<a name='L127'></a><a href='#L127'>127</a>
<a name='L128'></a><a href='#L128'>128</a>
<a name='L129'></a><a href='#L129'>129</a>
<a name='L130'></a><a href='#L130'>130</a>
<a name='L131'></a><a href='#L131'>131</a>
<a name='L132'></a><a href='#L132'>132</a>
<a name='L133'></a><a href='#L133'>133</a>
<a name='L134'></a><a href='#L134'>134</a>
<a name='L135'></a><a href='#L135'>135</a>
<a name='L136'></a><a href='#L136'>136</a>
<a name='L137'></a><a href='#L137'>137</a>
<a name='L138'></a><a href='#L138'>138</a>
<a name='L139'></a><a href='#L139'>139</a>
<a name='L140'></a><a href='#L140'>140</a>
<a name='L141'></a><a href='#L141'>141</a>
<a name='L142'></a><a href='#L142'>142</a>
<a name='L143'></a><a href='#L143'>143</a>
<a name='L144'></a><a href='#L144'>144</a>
<a name='L145'></a><a href='#L145'>145</a>
<a name='L146'></a><a href='#L146'>146</a>
<a name='L147'></a><a href='#L147'>147</a>
<a name='L148'></a><a href='#L148'>148</a>
<a name='L149'></a><a href='#L149'>149</a>
<a name='L150'></a><a href='#L150'>150</a>
<a name='L151'></a><a href='#L151'>151</a>
<a name='L152'></a><a href='#L152'>152</a>
<a name='L153'></a><a href='#L153'>153</a>
<a name='L154'></a><a href='#L154'>154</a>
<a name='L155'></a><a href='#L155'>155</a>
<a name='L156'></a><a href='#L156'>156</a>
<a name='L157'></a><a href='#L157'>157</a>
<a name='L158'></a><a href='#L158'>158</a>
<a name='L159'></a><a href='#L159'>159</a>
<a name='L160'></a><a href='#L160'>160</a>
<a name='L161'></a><a href='#L161'>161</a>
<a name='L162'></a><a href='#L162'>162</a>
<a name='L163'></a><a href='#L163'>163</a>
<a name='L164'></a><a href='#L164'>164</a>
<a name='L165'></a><a href='#L165'>165</a>
<a name='L166'></a><a href='#L166'>166</a>
<a name='L167'></a><a href='#L167'>167</a>
<a name='L168'></a><a href='#L168'>168</a>
<a name='L169'></a><a href='#L169'>169</a>
<a name='L170'></a><a href='#L170'>170</a>
<a name='L171'></a><a href='#L171'>171</a>
<a name='L172'></a><a href='#L172'>172</a>
<a name='L173'></a><a href='#L173'>173</a>
<a name='L174'></a><a href='#L174'>174</a>
<a name='L175'></a><a href='#L175'>175</a>
<a name='L176'></a><a href='#L176'>176</a>
<a name='L177'></a><a href='#L177'>177</a>
<a name='L178'></a><a href='#L178'>178</a>
<a name='L179'></a><a href='#L179'>179</a>
<a name='L180'></a><a href='#L180'>180</a>
<a name='L181'></a><a href='#L181'>181</a>
<a name='L182'></a><a href='#L182'>182</a>
<a name='L183'></a><a href='#L183'>183</a>
<a name='L184'></a><a href='#L184'>184</a>
<a name='L185'></a><a href='#L185'>185</a>
<a name='L186'></a><a href='#L186'>186</a>
<a name='L187'></a><a href='#L187'>187</a>
<a name='L188'></a><a href='#L188'>188</a>
<a name='L189'></a><a href='#L189'>189</a>
<a name='L190'></a><a href='#L190'>190</a>
<a name='L191'></a><a href='#L191'>191</a>
<a name='L192'></a><a href='#L192'>192</a>
<a name='L193'></a><a href='#L193'>193</a>
<a name='L194'></a><a href='#L194'>194</a>
<a name='L195'></a><a href='#L195'>195</a>
<a name='L196'></a><a href='#L196'>196</a>
<a name='L197'></a><a href='#L197'>197</a>
<a name='L198'></a><a href='#L198'>198</a>
<a name='L199'></a><a href='#L199'>199</a>
<a name='L200'></a><a href='#L200'>200</a>
<a name='L201'></a><a href='#L201'>201</a>
<a name='L202'></a><a href='#L202'>202</a>
<a name='L203'></a><a href='#L203'>203</a>
<a name='L204'></a><a href='#L204'>204</a>
<a name='L205'></a><a href='#L205'>205</a>
<a name='L206'></a><a href='#L206'>206</a>
<a name='L207'></a><a href='#L207'>207</a>
<a name='L208'></a><a href='#L208'>208</a>
<a name='L209'></a><a href='#L209'>209</a>
<a name='L210'></a><a href='#L210'>210</a>
<a name='L211'></a><a href='#L211'>211</a>
<a name='L212'></a><a href='#L212'>212</a>
<a name='L213'></a><a href='#L213'>213</a>
<a name='L214'></a><a href='#L214'>214</a>
<a name='L215'></a><a href='#L215'>215</a>
<a name='L216'></a><a href='#L216'>216</a>
<a name='L217'></a><a href='#L217'>217</a>
<a name='L218'></a><a href='#L218'>218</a>
<a name='L219'></a><a href='#L219'>219</a>
<a name='L220'></a><a href='#L220'>220</a>
<a name='L221'></a><a href='#L221'>221</a>
<a name='L222'></a><a href='#L222'>222</a>
<a name='L223'></a><a href='#L223'>223</a>
<a name='L224'></a><a href='#L224'>224</a>
<a name='L225'></a><a href='#L225'>225</a>
<a name='L226'></a><a href='#L226'>226</a>
<a name='L227'></a><a href='#L227'>227</a>
<a name='L228'></a><a href='#L228'>228</a>
<a name='L229'></a><a href='#L229'>229</a>
<a name='L230'></a><a href='#L230'>230</a>
<a name='L231'></a><a href='#L231'>231</a>
<a name='L232'></a><a href='#L232'>232</a>
<a name='L233'></a><a href='#L233'>233</a>
<a name='L234'></a><a href='#L234'>234</a>
<a name='L235'></a><a href='#L235'>235</a>
<a name='L236'></a><a href='#L236'>236</a>
<a name='L237'></a><a href='#L237'>237</a>
<a name='L238'></a><a href='#L238'>238</a>
<a name='L239'></a><a href='#L239'>239</a>
<a name='L240'></a><a href='#L240'>240</a>
<a name='L241'></a><a href='#L241'>241</a>
<a name='L242'></a><a href='#L242'>242</a>
<a name='L243'></a><a href='#L243'>243</a>
<a name='L244'></a><a href='#L244'>244</a>
<a name='L245'></a><a href='#L245'>245</a>
<a name='L246'></a><a href='#L246'>246</a>
<a name='L247'></a><a href='#L247'>247</a>
<a name='L248'></a><a href='#L248'>248</a>
<a name='L249'></a><a href='#L249'>249</a>
<a name='L250'></a><a href='#L250'>250</a>
<a name='L251'></a><a href='#L251'>251</a>
<a name='L252'></a><a href='#L252'>252</a>
<a name='L253'></a><a href='#L253'>253</a>
<a name='L254'></a><a href='#L254'>254</a>
<a name='L255'></a><a href='#L255'>255</a>
<a name='L256'></a><a href='#L256'>256</a>
<a name='L257'></a><a href='#L257'>257</a>
<a name='L258'></a><a href='#L258'>258</a>
<a name='L259'></a><a href='#L259'>259</a>
<a name='L260'></a><a href='#L260'>260</a>
<a name='L261'></a><a href='#L261'>261</a>
<a name='L262'></a><a href='#L262'>262</a>
<a name='L263'></a><a href='#L263'>263</a>
<a name='L264'></a><a href='#L264'>264</a>
<a name='L265'></a><a href='#L265'>265</a>
<a name='L266'></a><a href='#L266'>266</a>
<a name='L267'></a><a href='#L267'>267</a>
<a name='L268'></a><a href='#L268'>268</a>
<a name='L269'></a><a href='#L269'>269</a>
<a name='L270'></a><a href='#L270'>270</a>
<a name='L271'></a><a href='#L271'>271</a>
<a name='L272'></a><a href='#L272'>272</a>
<a name='L273'></a><a href='#L273'>273</a>
<a name='L274'></a><a href='#L274'>274</a>
<a name='L275'></a><a href='#L275'>275</a>
<a name='L276'></a><a href='#L276'>276</a>
<a name='L277'></a><a href='#L277'>277</a>
<a name='L278'></a><a href='#L278'>278</a>
<a name='L279'></a><a href='#L279'>279</a>
<a name='L280'></a><a href='#L280'>280</a>
<a name='L281'></a><a href='#L281'>281</a>
<a name='L282'></a><a href='#L282'>282</a>
<a name='L283'></a><a href='#L283'>283</a>
<a name='L284'></a><a href='#L284'>284</a>
<a name='L285'></a><a href='#L285'>285</a>
<a name='L286'></a><a href='#L286'>286</a>
<a name='L287'></a><a href='#L287'>287</a>
<a name='L288'></a><a href='#L288'>288</a>
<a name='L289'></a><a href='#L289'>289</a>
<a name='L290'></a><a href='#L290'>290</a>
<a name='L291'></a><a href='#L291'>291</a>
<a name='L292'></a><a href='#L292'>292</a>
<a name='L293'></a><a href='#L293'>293</a>
<a name='L294'></a><a href='#L294'>294</a>
<a name='L295'></a><a href='#L295'>295</a>
<a name='L296'></a><a href='#L296'>296</a>
<a name='L297'></a><a href='#L297'>297</a>
<a name='L298'></a><a href='#L298'>298</a>
<a name='L299'></a><a href='#L299'>299</a>
<a name='L300'></a><a href='#L300'>300</a>
<a name='L301'></a><a href='#L301'>301</a>
<a name='L302'></a><a href='#L302'>302</a>
<a name='L303'></a><a href='#L303'>303</a>
<a name='L304'></a><a href='#L304'>304</a>
<a name='L305'></a><a href='#L305'>305</a>
<a name='L306'></a><a href='#L306'>306</a>
<a name='L307'></a><a href='#L307'>307</a>
<a name='L308'></a><a href='#L308'>308</a>
<a name='L309'></a><a href='#L309'>309</a>
<a name='L310'></a><a href='#L310'>310</a>
<a name='L311'></a><a href='#L311'>311</a>
<a name='L312'></a><a href='#L312'>312</a>
<a name='L313'></a><a href='#L313'>313</a>
<a name='L314'></a><a href='#L314'>314</a>
<a name='L315'></a><a href='#L315'>315</a>
<a name='L316'></a><a href='#L316'>316</a>
<a name='L317'></a><a href='#L317'>317</a>
<a name='L318'></a><a href='#L318'>318</a>
<a name='L319'></a><a href='#L319'>319</a>
<a name='L320'></a><a href='#L320'>320</a>
<a name='L321'></a><a href='#L321'>321</a>
<a name='L322'></a><a href='#L322'>322</a>
<a name='L323'></a><a href='#L323'>323</a>
<a name='L324'></a><a href='#L324'>324</a>
<a name='L325'></a><a href='#L325'>325</a>
<a name='L326'></a><a href='#L326'>326</a>
<a name='L327'></a><a href='#L327'>327</a>
<a name='L328'></a><a href='#L328'>328</a>
<a name='L329'></a><a href='#L329'>329</a>
<a name='L330'></a><a href='#L330'>330</a>
<a name='L331'></a><a href='#L331'>331</a>
<a name='L332'></a><a href='#L332'>332</a>
<a name='L333'></a><a href='#L333'>333</a>
<a name='L334'></a><a href='#L334'>334</a>
<a name='L335'></a><a href='#L335'>335</a>
<a name='L336'></a><a href='#L336'>336</a>
<a name='L337'></a><a href='#L337'>337</a>
<a name='L338'></a><a href='#L338'>338</a>
<a name='L339'></a><a href='#L339'>339</a>
<a name='L340'></a><a href='#L340'>340</a>
<a name='L341'></a><a href='#L341'>341</a>
<a name='L342'></a><a href='#L342'>342</a>
<a name='L343'></a><a href='#L343'>343</a>
<a name='L344'></a><a href='#L344'>344</a>
<a name='L345'></a><a href='#L345'>345</a>
<a name='L346'></a><a href='#L346'>346</a>
<a name='L347'></a><a href='#L347'>347</a>
<a name='L348'></a><a href='#L348'>348</a>
<a name='L349'></a><a href='#L349'>349</a>
<a name='L350'></a><a href='#L350'>350</a>
<a name='L351'></a><a href='#L351'>351</a>
<a name='L352'></a><a href='#L352'>352</a>
<a name='L353'></a><a href='#L353'>353</a>
<a name='L354'></a><a href='#L354'>354</a>
<a name='L355'></a><a href='#L355'>355</a>
<a name='L356'></a><a href='#L356'>356</a>
<a name='L357'></a><a href='#L357'>357</a>
<a name='L358'></a><a href='#L358'>358</a>
<a name='L359'></a><a href='#L359'>359</a>
<a name='L360'></a><a href='#L360'>360</a>
<a name='L361'></a><a href='#L361'>361</a>
<a name='L362'></a><a href='#L362'>362</a>
<a name='L363'></a><a href='#L363'>363</a>
<a name='L364'></a><a href='#L364'>364</a>
<a name='L365'></a><a href='#L365'>365</a>
<a name='L366'></a><a href='#L366'>366</a>
<a name='L367'></a><a href='#L367'>367</a>
<a name='L368'></a><a href='#L368'>368</a>
<a name='L369'></a><a href='#L369'>369</a>
<a name='L370'></a><a href='#L370'>370</a>
<a name='L371'></a><a href='#L371'>371</a>
<a name='L372'></a><a href='#L372'>372</a>
<a name='L373'></a><a href='#L373'>373</a>
<a name='L374'></a><a href='#L374'>374</a>
<a name='L375'></a><a href='#L375'>375</a>
<a name='L376'></a><a href='#L376'>376</a>
<a name='L377'></a><a href='#L377'>377</a>
<a name='L378'></a><a href='#L378'>378</a>
<a name='L379'></a><a href='#L379'>379</a>
<a name='L380'></a><a href='#L380'>380</a>
<a name='L381'></a><a href='#L381'>381</a>
<a name='L382'></a><a href='#L382'>382</a>
<a name='L383'></a><a href='#L383'>383</a>
<a name='L384'></a><a href='#L384'>384</a>
<a name='L385'></a><a href='#L385'>385</a>
<a name='L386'></a><a href='#L386'>386</a>
<a name='L387'></a><a href='#L387'>387</a>
<a name='L388'></a><a href='#L388'>388</a>
<a name='L389'></a><a href='#L389'>389</a>
<a name='L390'></a><a href='#L390'>390</a>
<a name='L391'></a><a href='#L391'>391</a>
<a name='L392'></a><a href='#L392'>392</a>
<a name='L393'></a><a href='#L393'>393</a>
<a name='L394'></a><a href='#L394'>394</a>
<a name='L395'></a><a href='#L395'>395</a>
<a name='L396'></a><a href='#L396'>396</a>
<a name='L397'></a><a href='#L397'>397</a>
<a name='L398'></a><a href='#L398'>398</a>
<a name='L399'></a><a href='#L399'>399</a>
<a name='L400'></a><a href='#L400'>400</a>
<a name='L401'></a><a href='#L401'>401</a>
<a name='L402'></a><a href='#L402'>402</a>
<a name='L403'></a><a href='#L403'>403</a>
<a name='L404'></a><a href='#L404'>404</a>
<a name='L405'></a><a href='#L405'>405</a>
<a name='L406'></a><a href='#L406'>406</a>
<a name='L407'></a><a href='#L407'>407</a>
<a name='L408'></a><a href='#L408'>408</a>
<a name='L409'></a><a href='#L409'>409</a>
<a name='L410'></a><a href='#L410'>410</a>
<a name='L411'></a><a href='#L411'>411</a>
<a name='L412'></a><a href='#L412'>412</a>
<a name='L413'></a><a href='#L413'>413</a>
<a name='L414'></a><a href='#L414'>414</a>
<a name='L415'></a><a href='#L415'>415</a>
<a name='L416'></a><a href='#L416'>416</a>
<a name='L417'></a><a href='#L417'>417</a>
<a name='L418'></a><a href='#L418'>418</a>
<a name='L419'></a><a href='#L419'>419</a>
<a name='L420'></a><a href='#L420'>420</a>
<a name='L421'></a><a href='#L421'>421</a>
<a name='L422'></a><a href='#L422'>422</a>
<a name='L423'></a><a href='#L423'>423</a>
<a name='L424'></a><a href='#L424'>424</a>
<a name='L425'></a><a href='#L425'>425</a>
<a name='L426'></a><a href='#L426'>426</a>
<a name='L427'></a><a href='#L427'>427</a>
<a name='L428'></a><a href='#L428'>428</a>
<a name='L429'></a><a href='#L429'>429</a>
<a name='L430'></a><a href='#L430'>430</a>
<a name='L431'></a><a href='#L431'>431</a>
<a name='L432'></a><a href='#L432'>432</a>
<a name='L433'></a><a href='#L433'>433</a>
<a name='L434'></a><a href='#L434'>434</a>
<a name='L435'></a><a href='#L435'>435</a>
<a name='L436'></a><a href='#L436'>436</a>
<a name='L437'></a><a href='#L437'>437</a>
<a name='L438'></a><a href='#L438'>438</a>
<a name='L439'></a><a href='#L439'>439</a>
<a name='L440'></a><a href='#L440'>440</a>
<a name='L441'></a><a href='#L441'>441</a>
<a name='L442'></a><a href='#L442'>442</a>
<a name='L443'></a><a href='#L443'>443</a>
<a name='L444'></a><a href='#L444'>444</a>
<a name='L445'></a><a href='#L445'>445</a>
<a name='L446'></a><a href='#L446'>446</a>
<a name='L447'></a><a href='#L447'>447</a>
<a name='L448'></a><a href='#L448'>448</a>
<a name='L449'></a><a href='#L449'>449</a>
<a name='L450'></a><a href='#L450'>450</a>
<a name='L451'></a><a href='#L451'>451</a>
<a name='L452'></a><a href='#L452'>452</a>
<a name='L453'></a><a href='#L453'>453</a>
<a name='L454'></a><a href='#L454'>454</a>
<a name='L455'></a><a href='#L455'>455</a>
<a name='L456'></a><a href='#L456'>456</a>
<a name='L457'></a><a href='#L457'>457</a>
<a name='L458'></a><a href='#L458'>458</a>
<a name='L459'></a><a href='#L459'>459</a>
<a name='L460'></a><a href='#L460'>460</a>
<a name='L461'></a><a href='#L461'>461</a>
<a name='L462'></a><a href='#L462'>462</a>
<a name='L463'></a><a href='#L463'>463</a>
<a name='L464'></a><a href='#L464'>464</a>
<a name='L465'></a><a href='#L465'>465</a>
<a name='L466'></a><a href='#L466'>466</a>
<a name='L467'></a><a href='#L467'>467</a>
<a name='L468'></a><a href='#L468'>468</a>
<a name='L469'></a><a href='#L469'>469</a>
<a name='L470'></a><a href='#L470'>470</a>
<a name='L471'></a><a href='#L471'>471</a>
<a name='L472'></a><a href='#L472'>472</a>
<a name='L473'></a><a href='#L473'>473</a>
<a name='L474'></a><a href='#L474'>474</a>
<a name='L475'></a><a href='#L475'>475</a>
<a name='L476'></a><a href='#L476'>476</a>
<a name='L477'></a><a href='#L477'>477</a>
<a name='L478'></a><a href='#L478'>478</a>
<a name='L479'></a><a href='#L479'>479</a>
<a name='L480'></a><a href='#L480'>480</a>
<a name='L481'></a><a href='#L481'>481</a>
<a name='L482'></a><a href='#L482'>482</a>
<a name='L483'></a><a href='#L483'>483</a>
<a name='L484'></a><a href='#L484'>484</a>
<a name='L485'></a><a href='#L485'>485</a>
<a name='L486'></a><a href='#L486'>486</a>
<a name='L487'></a><a href='#L487'>487</a>
<a name='L488'></a><a href='#L488'>488</a>
<a name='L489'></a><a href='#L489'>489</a>
<a name='L490'></a><a href='#L490'>490</a>
<a name='L491'></a><a href='#L491'>491</a>
<a name='L492'></a><a href='#L492'>492</a>
<a name='L493'></a><a href='#L493'>493</a>
<a name='L494'></a><a href='#L494'>494</a>
<a name='L495'></a><a href='#L495'>495</a>
<a name='L496'></a><a href='#L496'>496</a>
<a name='L497'></a><a href='#L497'>497</a>
<a name='L498'></a><a href='#L498'>498</a>
<a name='L499'></a><a href='#L499'>499</a>
<a name='L500'></a><a href='#L500'>500</a>
<a name='L501'></a><a href='#L501'>501</a>
<a name='L502'></a><a href='#L502'>502</a>
<a name='L503'></a><a href='#L503'>503</a>
<a name='L504'></a><a href='#L504'>504</a>
<a name='L505'></a><a href='#L505'>505</a></td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">'use client'
&nbsp;
import Link from 'next/link'
import React, { useEffect,useState } from <span class="cstat-no" title="statement not covered" >'react'</span>
import { motion } from <span class="cstat-no" title="statement not covered" >"framer-motion"</span>
import Image from 'next/image'
import { FaArrowRight } from <span class="cstat-no" title="statement not covered" >'react-icons/fa'</span>
import { Event } from '@/types'
import { ApiErrorBoundary } from '@/components/api-error-boundary'
import { LazyLandingScroll, LandingScrollSkeleton, LazyComponent } from <span class="cstat-no" title="statement not covered" >'@/components/lazy-loading'</span>
import { usePerformanceMonitoring, useRenderTime } from <span class="cstat-no" title="statement not covered" >'@/hooks/use-performance'</span>
&nbsp;
&nbsp;
&nbsp;
const MembersIcon = () =&gt; (
  &lt;svg className="text-gfgsc-green w-4 h-4 sm:w-5 sm:h-5" viewBox="0 0 24 24" fill="currentColor"&gt;
    &lt;path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z" /&gt;
  &lt;/svg&gt;
)
&nbsp;
const EventsIcon = () =&gt; (
  &lt;svg className="text-gfgsc-green w-4 h-4 sm:w-5 sm:h-5" viewBox="0 0 24 24" fill="currentColor"&gt;
    &lt;path d="M17 12h-5v5h5v-5zM16 1v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2h-1V1h-2zm3 18H5V8h14v11z" /&gt;
  &lt;/svg&gt;
)
&nbsp;
const ContestsIcon = () =&gt; (
  &lt;svg className="text-gfgsc-green w-4 h-4 sm:w-5 sm:h-5" viewBox="0 0 24 24" fill="currentColor"&gt;
    &lt;path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z" /&gt;
  &lt;/svg&gt;
)
&nbsp;
const LearnIcon = () =&gt; (
  &lt;svg className="text-gfgsc-green w-6 h-6" viewBox="0 0 24 24" fill="currentColor"&gt;
    &lt;path d="M12 3L1 9l4 2.18v6L12 21l7-3.82v-6l2-1.09V17h2V9L12 3zm6.82 6L12 12.72 5.18 9 12 5.28 18.82 9zM17 15.99l-5 2.73-5-2.73v-3.72L12 15l5-2.73v3.72z" /&gt;
  &lt;/svg&gt;
)
&nbsp;
const BuildIcon = () =&gt; (
  &lt;svg className="text-gfgsc-green w-6 h-6" viewBox="0 0 24 24" fill="currentColor"&gt;
    &lt;path d="M22.7 19l-9.1-9.1c.9-2.3.4-5-1.5-6.9-2-2-5-2.4-7.4-1.3L9 6 6 9 1.6 4.7C.4 7.1.9 10.1 2.9 12.1c1.9 1.9 4.6 2.4 6.9 1.5l9.1 9.1c.4.4 1 .4 1.4 0l2.3-2.3c.5-.4.5-1.1.1-1.4z" /&gt;
  &lt;/svg&gt;
)
&nbsp;
const ConnectIcon = () =&gt; (
  &lt;svg className="text-gfgsc-green w-6 h-6" viewBox="0 0 24 24" fill="currentColor"&gt;
    &lt;path d="M16 11c1.66 0 2.99-1.34 2.99-3S17.66 5 16 5c-1.66 0-3 1.34-3 3s1.34 3 3 3zm-8 0c1.66 0 2.99-1.34 2.99-3S9.66 5 8 5C6.34 5 5 6.34 5 8s1.34 3 3 3zm0 2c-2.33 0-7 1.17-7 3.5V19h14v-2.5c0-2.33-4.67-3.5-7-3.5zm8 0c-.29 0-.62.02-.97.05 1.16.84 1.97 1.97 1.97 3.45V19h6v-2.5c0-2.33-4.67-3.5-7-3.5z" /&gt;
  &lt;/svg&gt;
)
&nbsp;
const Icon = () =&gt; (
  &lt;svg
          stroke="currentColor"
          fill="currentColor"
          strokeWidth="0"
          role="img"
          viewBox="0 0 24 24"
          className="text-gfgsc-green w-8 h-8 sm:w-10 sm:h-10"
          height="1em"
          width="1em"
          xmlns="http://www.w3.org/2000/svg"
        &gt;
          &lt;path d="M21.45 14.315c-.143.28-.334.532-.565.745a3.691 3.691 0 0 1-1.104.695 4.51 4.51 0 0 1-3.116-.016 3.79 3.79 0 0 1-2.135-2.078 3.571 3.571 0 0 1-.13-.353h7.418a4.26 4.26 0 0 1-.368 1.008zm-11.99-.654a3.793 3.793 0 0 1-2.134 2.078 4.51 4.51 0 0 1-3.117.016 3.7 3.7 0 0 1-1.104-.695 2.652 2.652 0 0 1-.564-.745 4.221 4.221 0 0 1-.368-1.006H9.59c-.038.12-.08.238-.13.352zm14.501-1.758a3.849 3.849 0 0 0-.082-.475l-9.634-.008a3.932 3.932 0 0 1 1.143-2.348c.363-.35.79-.625 1.26-.809a3.97 3.97 0 0 1 4.484.957l1.521-1.49a5.7 5.7 0 0 0-1.922-1.357 6.283 6.283 0 0 0-2.544-.49 6.35 6.35 0 0 0-2.405.457 6.007 6.007 0 0 0-1.963 1.276 6.142 6.142 0 0 0-1.325 1.94 5.862 5.862 0 0 0-.466 1.864h-.063a5.857 5.857 0 0 0-.467-1.865 6.13 6.13 0 0 0-1.325-1.939A6 6 0 0 0 8.21 6.34a6.698 6.698 0 0 0-4.949.031A5.708 5.708 0 0 0 1.34 7.73l1.52 1.49a4.166 4.166 0 0 1 4.484-.958c.47.184.898.46 1.26.81.368.36.66.792.859 1.268.146.344.242.708.285 1.08l-9.635.008A4.714 4.714 0 0 0 0 12.457a6.493 6.493 0 0 0 .345 2.127 4.927 4.927 0 0 0 1.08 1.783c.528.56 1.17 1 1.88 1.293a6.454 6.454 0 0 0 2.504.457c.824.005 1.64-.15 2.404-.457a5.986 5.986 0 0 0 1.964-1.277 6.116 6.116 0 0 0 1.686-3.076h.273a6.13 6.13 0 0 0 1.686 3.077 5.99 5.99 0 0 0 1.964 1.276 6.345 6.345 0 0 0 2.405.457 6.45 6.45 0 0 0 2.502-.457 5.42 5.42 0 0 0 1.882-1.293 4.928 4.928 0 0 0 1.08-1.783A6.52 6.52 0 0 0 24 12.457a4.757 4.757 0 0 0-.039-.554z"&gt;&lt;/path&gt;
        &lt;/svg&gt;
)
&nbsp;
const containerVariants = <span class="cstat-no" title="statement not covered" >{</span>
  hidden: { opacity: 0, y: 50 },
  visible: { opacity: 1, y: 0, transition: { duration: 1, staggerChildren: 0.2 } },
}
&nbsp;
const childVariants = <span class="cstat-no" title="statement not covered" >{</span>
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0, transition: { duration: 0.8 } },
}
&nbsp;
const buttonHover = <span class="cstat-no" title="statement not covered" >{</span>
  scale: 1.05,
  transition: { duration: 0.3 },
}
&nbsp;
export default function <span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >Home() {</span></span>
  const [events, setEvents] = <span class="cstat-no" title="statement not covered" >useState&lt;Event[]&gt;([])</span>
&nbsp;
  // Performance monitoring
<span class="cstat-no" title="statement not covered" >  usePerformanceMonitoring(true)</span>
<span class="cstat-no" title="statement not covered" >  useRenderTime('HomePage')</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  useEffect(<span class="fstat-no" title="function not covered" >() =&gt; {</span></span>
<span class="cstat-no" title="statement not covered" >    window.onload = <span class="fstat-no" title="function not covered" >() =&gt; <span class="cstat-no" title="statement not covered" >w</span>indow.scrollTo(0, 0)</span></span>
  }, [])
&nbsp;
<span class="cstat-no" title="statement not covered" >  useEffect(<span class="fstat-no" title="function not covered" >() =&gt; {</span></span>
    const fetchEvents = <span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >async () =&gt; {</span></span>
<span class="cstat-no" title="statement not covered" >      try {</span>
        const res = <span class="cstat-no" title="statement not covered" >await fetch(`${process.env.NEXT_PUBLIC_API_BASE}/events`)</span>
        const data = <span class="cstat-no" title="statement not covered" >await res.json()</span>
<span class="cstat-no" title="statement not covered" >        setEvents(Array.isArray(data) ? data : [])</span>
      } catch (err) {
<span class="cstat-no" title="statement not covered" >        console.error("Failed to fetch events:", err)</span>
<span class="cstat-no" title="statement not covered" >        setEvents([])</span>
      }
    }
&nbsp;
<span class="cstat-no" title="statement not covered" >    fetchEvents()</span>
  }, [])
&nbsp;
  const pastEvents = <span class="cstat-no" title="statement not covered" >events.filter(<span class="fstat-no" title="function not covered" >event =&gt; {</span></span>
    const end = <span class="cstat-no" title="statement not covered" >event.date?.split(" to ")[1] || event.date</span>
<span class="cstat-no" title="statement not covered" >    return new Date(end) &lt; new Date()</span>
  })
&nbsp;
&nbsp;
  return (
    &lt;&gt;
      &lt;motion.div
        className="min-h-screen w-full flex items-center justify-center overflow-hidden bg-gradient-to-bl from-gray-50 via-green-50 to-green-100 dark:from-gray-900 dark:via-green-900 dark:to-green-800"
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      &gt;
        {/* Hero Section */}
        &lt;motion.div
          className="flex flex-col items-start ml-4 sm:ml-10 md:ml-20 space-y-4 sm:space-y-6"
          variants={containerVariants}
        &gt;
          {/* Icon and Subtitle */}
          &lt;motion.div className="flex items-center space-x-2" variants={childVariants}&gt;
            
          
            {[
              {
                icon: &lt;Icon /&gt;,
                text: "Student Chapter MIT ADT",
              },
            ].map(<span class="fstat-no" title="function not covered" >(i</span>tem, index) =&gt; (
<span class="cstat-no" title="statement not covered" >              &lt;motion.div</span>
                key={index}
                className="flex items-center space-x-2"
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: index * 0.2, duration: 0.5 }}
              &gt;
                {item.icon}
                &lt;span className="hero-text text-xs sm:text-md md:text-lg font-semibold text-gfgsc-green"&gt;
                  {item.text}
                &lt;/span&gt;
              &lt;/motion.div&gt;
            ))}
            &lt;/motion.div&gt;
&nbsp;
          {/* New Heading */}
          &lt;motion.h1
            className="hero-text text-xl sm:text-2xl md:text-4xl lg:text-6xl font-semibold md:font-bold text-gray-800 dark:text-gray-100 leading-tight"
            variants={childVariants}
          &gt;
            Where Code Meets &lt;span className="text-gfgsc-green"&gt;Community&lt;/span&gt;
          &lt;/motion.h1&gt;
&nbsp;
          {/* Description */}
          &lt;motion.p
            className="hero-text sm:text-sm md:text-lg text-gray-600 dark:text-gray-300 py-2 w-[300px] sm:w-[400px] md:w-[500px] lg:w-[600px]"
            variants={childVariants}
          &gt;
            Join MIT ADT's premier coding community. Learn, grow, and build together with fellow tech
            enthusiasts guided by GeeksforGeeks.
          &lt;/motion.p&gt;
&nbsp;
          {/* Buttons */}
          &lt;motion.div
            className="hero-text flex flex-wrap gap-2 sm:gap-4"
            variants={childVariants}
          &gt;
            &lt;motion.a
              href="https://forms.gle/A9suayWQENsF9QLn9"
              target="_blank"
              className="px-4 sm:px-6 py-2.5 sm:py-3 bg-gfgsc-green text-white rounded-lg font-semibold shadow-lg hover:bg-opacity-95 transform hover:scale-105 transition-all text-sm sm:text-base no-underline"
              whileHover={buttonHover}
            &gt;
              Join Chapter
            &lt;/motion.a&gt;
            &lt;motion.button
              className="px-4 sm:px-6 py-2.5 sm:py-3 border-2 border-gfgsc-green text-gfgsc-green rounded-lg font-semibold hover:bg-green-50 dark:hover:bg-green-900 transform hover:scale-105 transition-all text-sm sm:text-base"
              whileHover={buttonHover}
            &gt;
              Learn More
            &lt;/motion.button&gt;
          &lt;/motion.div&gt;
&nbsp;
          {/* Info Section */}
          &lt;div className="flex flex-wrap items-center justify-center gap-2 sm:gap-4 md:gap-8 pt-4"&gt;
            {[
              {
                icon: &lt;MembersIcon /&gt;,
                text: "40+ Members",
              },
              {
                icon: &lt;EventsIcon /&gt;,
                text: "5+ Events",
              },
              {
                icon: &lt;ContestsIcon /&gt;,
                text: "Regular Contests",
              },
            ].map(<span class="fstat-no" title="function not covered" >(i</span>tem, index) =&gt; (
<span class="cstat-no" title="statement not covered" >              &lt;motion.div</span>
                key={index}
                className="flex items-center space-x-2"
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: index * 0.2, duration: 0.5 }}
              &gt;
                {item.icon}
                &lt;span className="hero-text text-xs sm:text-sm font-medium text-gray-600 dark:text-gray-300"&gt;
                  {item.text}
                &lt;/span&gt;
              &lt;/motion.div&gt;
            ))}
          &lt;/div&gt;
        &lt;/motion.div&gt;
&nbsp;
        {/* Right Side (Animated Gradients) */}
        &lt;div className="relative flex flex-col justify-center items-center w-full md:w-1/2"&gt;
          &lt;div 
            className="absolute -top-20 -left-30 sm:-top-30 w-32 sm:w-48 md:w-64 h-32 sm:h-48 md:h-64 bg-green-200 dark:bg-green-700 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-gradientBefore"&gt;
          &lt;/div&gt;
          &lt;div 
            className="absolute -top-40 -left-30 sm:-top-60  w-32 sm:w-48 md:w-64 h-32 sm:h-48 md:h-64 bg-green-400 dark:bg-green-600 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-gradientAfter"&gt;
          &lt;/div&gt;
        &lt;/div&gt;
      &lt;/motion.div&gt;
      
      {/*second Section*/}
      &lt;LazyComponent fallback={&lt;LandingScrollSkeleton /&gt;}&gt;
        &lt;LazyLandingScroll /&gt;
      &lt;/LazyComponent&gt;
      &lt;div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8"&gt;
        &lt;motion.div
          className="text-center mt-16 mb-16"
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        &gt;
          &lt;motion.div
            style={{ opacity: 1, transform: 'none' }}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 1, delay: 0.2 }}
          &gt;
            &lt;motion.h2
              className="text-2xl sm:text-4xl md:text-5xl font-bold text-gray-900 dark:text-gray-100 mb-4 md:mb-5"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 1, delay: 0.4 }}
            &gt;
              Where Knowledge Meets
              &lt;span className="text-gfgsc-green"&gt; Opportunity&lt;/span&gt;
            &lt;/motion.h2&gt;
            &lt;motion.div
              className="w-20 md:w-32 h-1 md:h-1.5 bg-gfgsc-green mx-auto rounded-full mb-6"
              initial={{ width: 0 }}
              animate={{ width: '13%' }}
              transition={{ duration: 1, delay: 0.6 }}
            &gt;&lt;/motion.div&gt;
            &lt;motion.p
              className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 1, delay: 0.8 }}
            &gt;
              The GeeksforGeeks Student Chapter provides structured opportunities
              for individuals to enhance their skills across various domains,
              fostering professional growth and preparing them for successful
              careers by joining top multinational companies.
            &lt;/motion.p&gt;
          &lt;/motion.div&gt;
        &lt;/motion.div&gt;
&nbsp;
        {/* Grid section with cards */}
        &lt;div className="grid grid-cols-1 md:grid-cols-3 gap-8"&gt;
          {/* Learn card */}
          &lt;div className="group relative bg-white dark:bg-gray-800 p-6 rounded-xl border border-gray-100 dark:border-gray-700 shadow-sm transition-all duration-300 overflow-hidden hover:shadow-lg hover:scale-105 hover:border-green-200 dark:hover:border-green-700"&gt;
            &lt;div className="flex items-center justify-center w-12 h-12 bg-green-50 dark:bg-green-900 rounded-lg mb-4 group-hover:scale-110 transition-transform duration-300"&gt;
              &lt;LearnIcon /&gt;
            &lt;/div&gt;
            &lt;h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-2"&gt;Learn&lt;/h3&gt;
            &lt;p className="text-gray-600 dark:text-gray-300"&gt;Hone your competitive programming skills through contests and solution discussions.&lt;/p&gt;
            &lt;div className="absolute bottom-0 left-0 w-full h-1 bg-gfgsc-green transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 rounded-b-sm"&gt;&lt;/div&gt;
          &lt;/div&gt;
&nbsp;
          {/* Build card */}
          &lt;div className="group relative bg-white dark:bg-gray-800 p-6 rounded-xl border border-gray-100 dark:border-gray-700 shadow-sm transition-all duration-300 overflow-hidden hover:shadow-lg hover:scale-105 hover:border-green-200 dark:hover:border-green-700"&gt;
            &lt;div className="flex items-center justify-center w-12 h-12 bg-green-50 dark:bg-green-900 rounded-lg mb-4 group-hover:scale-110 transition-transform duration-300"&gt;
              &lt;BuildIcon /&gt;
            &lt;/div&gt;
            &lt;h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-2"&gt;Build&lt;/h3&gt;
            &lt;p className="text-gray-600 dark:text-gray-300"&gt;Work on real-world projects and enhance your development skills.&lt;/p&gt;
            &lt;div className="absolute bottom-0 left-0 w-full h-1 bg-gfgsc-green transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 rounded-b-sm"&gt;&lt;/div&gt;
          &lt;/div&gt;
&nbsp;
          {/* Connect card */}
          &lt;div className="group relative bg-white dark:bg-gray-800 p-6 rounded-xl border border-gray-100 dark:border-gray-700 shadow-sm transition-all duration-300 overflow-hidden hover:shadow-lg hover:scale-105 hover:border-green-200 dark:hover:border-green-700"&gt;
            &lt;div className="flex items-center justify-center w-12 h-12 bg-green-50 dark:bg-green-900 rounded-lg mb-4 group-hover:scale-110 transition-transform duration-300"&gt;
              &lt;ConnectIcon /&gt;
            &lt;/div&gt;
            &lt;h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-2"&gt;Connect&lt;/h3&gt;
            &lt;p className="text-gray-600 dark:text-gray-300"&gt;Join a community of passionate student developers&lt;/p&gt;
            &lt;div className="absolute bottom-0 left-0 w-full h-1 bg-gfgsc-green transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 rounded-b-sm"&gt;&lt;/div&gt;
          &lt;/div&gt;
        &lt;/div&gt;
      &lt;/div&gt;
      &lt;div className="max-w-[90rem] mx-auto px-4 sm:px-6 lg:px-8"&gt;
        &lt;div className="py-6 sm:py-10 w-full flex justify-center px-0 sm:px-6 lg:px-18"&gt;
          &lt;div className="flex flex-col lg:flex-row w-full sm:gap-12 rounded-xl border border-gray-100 dark:border-gray-700 shadow-lg bg-white dark:bg-gray-800 overflow-hidden"&gt;
            &lt;div className="flex-1 p-8 lg:p-12 flex items-center"&gt;
              &lt;div className="max-w-xl space-y-8"&gt;
                &lt;div className="space-y-4"&gt;
                  &lt;div className="text-sm uppercase tracking-widest text-gfgsc-green font-medium"&gt;
                    The diversity of our club
                  &lt;/div&gt;
                  &lt;h2 className="text-4xl lg:text-5xl font-bold leading-tight text-gray-900 dark:text-gray-100"&gt;
                    &lt;span className="inline-block"&gt;Discover Your &lt;/span&gt;
                    &lt;div className="relative inline-block"&gt;
                      &lt;span className="inline-block"&gt;
                        &lt;span className="text-gfgsc-green ml-2"&gt;Tech Path&lt;/span&gt;
                      &lt;/span&gt;
                      &lt;div className="absolute -bottom-1 left-0 h-1 bg-green-200 dark:bg-green-700 w-full ml-2"&gt;&lt;/div&gt;
                    &lt;/div&gt;
                  &lt;/h2&gt;
                &lt;/div&gt;
                &lt;div className="prose prose-lg text-gray-600 dark:text-gray-300"&gt;
                  &lt;p className="leading-relaxed"&gt;
                    Join a vibrant community where innovation meets learning. Dive into multiple domains, collaborate with peers, and shape your technical journey with hands-on experience.
                  &lt;/p&gt;
                &lt;/div&gt;
                &lt;div className="flex flex-col sm:flex-row gap-4"&gt;
                  &lt;div className="inline-flex items-center py-3 px-6 text-lg font-medium text-gfgsc-green transition-all duration-300 group border-2 border-gfgsc-green rounded-full cursor-pointer hover:bg-green-600 hover:text-white dark:hover:text-gray-900 hover:scale-105"&gt;
                    Explore Domains
                    &lt;FaArrowRight
                      className="ml-2 text-xl transform group-hover:translate-x-2 transition-all duration-300 ease-in-out"
                      size="1em"
                    /&gt;
                  &lt;/div&gt;
                &lt;/div&gt;
              &lt;/div&gt;
            &lt;/div&gt;
            &lt;div className="flex-1 p-8 lg:p-12 bg-gray-50 dark:bg-gray-900"&gt;
              &lt;div className="grid gap-6 h-full"&gt;
                &lt;div className="p-6 rounded-xl bg-blue-50 dark:bg-blue-900 backdrop-blur-sm transition-all duration-300 hover:shadow-lg hover:scale-105 hover:bg-blue-100 dark:hover:bg-blue-800"&gt;
                  &lt;div className="flex items-center gap-4 mb-3 transition-transform duration-300 group-hover:scale-110"&gt;
                    &lt;svg
                      stroke="currentColor"
                      fill="currentColor"
                      strokeWidth="0"
                      viewBox="0 0 24 24"
                      className="text-2xl text-blue-500"
                      height="1em"
                      width="1em"
                      xmlns="http://www.w3.org/2000/svg"
                    &gt;
                      &lt;path d="M20 3H4c-1.103 0-2 .897-2 2v14c0 1.103.897 2 2 2h16c1.103 0 2-.897 2-2V5c0-1.103-.897-2-2-2zM4 19V7h16l.002 12H4z"&gt;&lt;/path&gt;
                      &lt;path d="M9.293 9.293 5.586 13l3.707 3.707 1.414-1.414L8.414 13l2.293-2.293zm5.414 0-1.414 1.414L15.586 13l-2.293 2.293 1.414 1.414L18.414 13z"&gt;&lt;/path&gt;
                    &lt;/svg&gt;
                    &lt;h3 className="font-semibold text-lg text-gray-900 dark:text-gray-100"&gt;Competitive Programming&lt;/h3&gt;
                  &lt;/div&gt;
                  &lt;div className="flex flex-wrap gap-2"&gt;
                    &lt;span className="px-3 py-1 text-sm rounded-full bg-white/80 dark:bg-gray-800/80 text-gray-700 dark:text-gray-300"&gt;DSA&lt;/span&gt;
                    &lt;span className="px-3 py-1 text-sm rounded-full bg-white/80 dark:bg-gray-800/80 text-gray-700 dark:text-gray-300"&gt;DP&lt;/span&gt;
                    &lt;span className="px-3 py-1 text-sm rounded-full bg-white/80 dark:bg-gray-800/80 text-gray-700 dark:text-gray-300"&gt;Problem Solving&lt;/span&gt;
                    &lt;span className="px-3 py-1 text-sm rounded-full bg-white/80 dark:bg-gray-800/80 text-gray-700 dark:text-gray-300"&gt;Algorithms&lt;/span&gt;
                  &lt;/div&gt;
                &lt;/div&gt;
&nbsp;
                &lt;div className="p-6 rounded-xl bg-green-50 dark:bg-green-900 backdrop-blur-sm transition-all duration-300 hover:shadow-lg hover:scale-105 hover:bg-green-100 dark:hover:bg-green-800"&gt;
                  &lt;div className="flex items-center gap-4 mb-3 transition-transform duration-300 group-hover:scale-110"&gt;
                    &lt;svg
                      stroke="currentColor"
                      fill="currentColor"
                      strokeWidth="0"
                      role="img"
                      viewBox="0 0 24 24"
                      className="text-2xl text-green-500"
                      height="1em"
                      width="1em"
                      xmlns="http://www.w3.org/2000/svg"
                    &gt;
                      &lt;path d="M22.1987 18.498l-9.7699 5.5022v-4.2855l6.0872-3.3338 3.6826 2.117zm.6683-.6026V6.3884l-3.5752 2.0544v7.396zm-21.0657.6026l9.7699 5.5022v-4.2855L5.484 16.3809l-3.6826 2.117zm-.6683-.6026V6.3884l3.5751 2.0544v7.396zm.4183-12.2515l10.0199-5.644v4.1434L5.152 7.6586l-.0489.028zm20.8975 0l-10.02-5.644v4.1434l6.4192 3.5154.0489.028 3.5518-2.0427zm-10.8775 13.096l-6.0056-3.2873V8.9384l6.0054 3.4525v6.349zm.8575 0l6.0053-3.2873V8.9384l-6.0053 3.4525zM5.9724 8.1845l6.0287-3.3015L18.03 8.1845l-6.0288 3.4665z"&gt;&lt;/path&gt;
                    &lt;/svg&gt;
                    &lt;h3 className="font-semibold text-lg text-gray-900 dark:text-gray-100"&gt;Web Development&lt;/h3&gt;
                  &lt;/div&gt;
                  &lt;div className="flex flex-wrap gap-2"&gt;
                    &lt;span className="px-3 py-1 text-sm rounded-full bg-white/80 dark:bg-gray-800/80 text-gray-700 dark:text-gray-300"&gt;Frontend&lt;/span&gt;
                    &lt;span className="px-3 py-1 text-sm rounded-full bg-white/80 dark:bg-gray-800/80 text-gray-700 dark:text-gray-300"&gt;Backend&lt;/span&gt;
                    &lt;span className="px-3 py-1 text-sm rounded-full bg-white/80 dark:bg-gray-800/80 text-gray-700 dark:text-gray-300"&gt;Full Stack&lt;/span&gt;
                    &lt;span className="px-3 py-1 text-sm rounded-full bg-white/80 dark:bg-gray-800/80 text-gray-700 dark:text-gray-300"&gt;DevOps&lt;/span&gt;
                  &lt;/div&gt;
                &lt;/div&gt;
&nbsp;
                &lt;div className="p-6 rounded-xl bg-purple-50 dark:bg-purple-900 backdrop-blur-sm transition-all duration-300 hover:shadow-lg hover:scale-105 hover:bg-purple-100 dark:hover:bg-purple-800"&gt;
                  &lt;div className="flex items-center gap-4 mb-3 transition-transform duration-300 group-hover:scale-110"&gt;
                    &lt;svg
                      stroke="currentColor"
                      fill="currentColor"
                      strokeWidth="0"
                      role="img"
                      viewBox="0 0 24 24"
                      className="text-2xl text-purple-500"
                      height="1em"
                      width="1em"
                      xmlns="http://www.w3.org/2000/svg"
                    &gt;
                      &lt;path d="M15.852 8.981h-4.588V0h4.588c2.476 0 4.49 2.014 4.49 4.49s-2.014 4.491-4.49 4.491zM12.735 7.51h3.117c1.665 0 3.019-1.355 3.019-3.019s-1.355-3.019-3.019-3.019h-3.117V7.51zm0 1.471H8.148c-2.476 0-4.49-2.014-4.49-4.49S5.672 0 8.148 0h4.588v8.981zm-4.587-7.51c-1.665 0-3.019 1.355-3.019 3.019s1.354 3.02 3.019 3.02h3.117V1.471H8.148zm4.587 15.019H8.148c-2.476 0-4.49-2.014-4.49-4.49s2.014-4.49 4.49-4.49h4.588v8.98zM8.148 8.981c-1.665 0-3.019 1.355-3.019 3.019s1.355 3.019 3.019 3.019h3.117V8.981H8.148zM8.172 24c-2.489 0-4.515-2.014-4.515-4.49s2.014-4.49 4.49-4.49h4.588v4.441c0 2.503-2.047 4.539-4.563 4.539zm-.024-7.51a3.023 3.023 0 0 0-3.019 3.019c0 1.665 1.365 3.019 3.044 3.019 1.705 0 3.093-1.376 3.093-3.068v-2.97H8.148zm7.704 0h-.098c-2.476 0-4.49-2.014-4.49-4.49s2.014-4.49 4.49-4.49h.098c2.476 0 4.49 2.014 4.49 4.49s-2.014 4.49-4.49 4.49zm-.097-7.509c-1.665 0-3.019 1.355-3.019 3.019s1.355 3.019 3.019 3.019h.098c1.665 0 3.019-1.355 3.019-3.019s-1.355-3.019-3.019-3.019h-.098z"&gt;&lt;/path&gt;
                    &lt;/svg&gt;
                    &lt;h3 className="font-semibold text-lg text-gray-900 dark:text-gray-100"&gt;Data Analytics&lt;/h3&gt;
                  &lt;/div&gt;
                  &lt;div className="flex flex-wrap gap-2"&gt;
                    &lt;span className="px-3 py-1 text-sm rounded-full bg-white/80 dark:bg-gray-800/80 text-gray-700 dark:text-gray-300"&gt;Statistics&lt;/span&gt;
                    &lt;span className="px-3 py-1 text-sm rounded-full bg-white/80 dark:bg-gray-800/80 text-gray-700 dark:text-gray-300"&gt;SQL&lt;/span&gt;
                    &lt;span className="px-3 py-1 text-sm rounded-full bg-white/80 dark:bg-gray-800/80 text-gray-700 dark:text-gray-300"&gt;Tableau&lt;/span&gt;
                    &lt;span className="px-3 py-1 text-sm rounded-full bg-white/80 dark:bg-gray-800/80 text-gray-700 dark:text-gray-300"&gt;Data Visualization&lt;/span&gt;
                  &lt;/div&gt;
                &lt;/div&gt;
&nbsp;
                &lt;div className="p-6 rounded-xl bg-orange-50 dark:bg-orange-900 backdrop-blur-sm transition-all duration-300 hover:shadow-lg hover:scale-105 hover:bg-orange-100 dark:hover:bg-orange-800"&gt;
                  &lt;div className="flex items-center gap-4 mb-3 transition-transform duration-300 group-hover:scale-110"&gt;
                    &lt;svg
                      stroke="currentColor"
                      fill="currentColor"
                      strokeWidth="0"
                      viewBox="0 0 24 24"
                      className="text-2xl text-orange-500"
                      height="1em"
                      width="1em"
                      xmlns="http://www.w3.org/2000/svg"
                    &gt;
                      &lt;path d="M1.292 5.856L11.54 0v24l-4.095-2.378V7.603l-6.168 3.564.015-5.31zm21.43 5.311l-.014-5.31L12.46 0v24l4.095-2.378V14.87l3.092 1.788-.018-4.618-3.074-1.756V7.603l6.168 3.564z"&gt;&lt;/path&gt;
                    &lt;/svg&gt;
                    &lt;h3 className="font-semibold text-lg text-gray-900 dark:text-gray-100"&gt;Machine Learning&lt;/h3&gt;
                  &lt;/div&gt;
                  &lt;div className="flex flex-wrap gap-2"&gt;
                    &lt;span className="px-3 py-1 text-sm rounded-full bg-white/80 dark:bg-gray-800/80 text-gray-700 dark:text-gray-300"&gt;Deep Learning&lt;/span&gt;
                    &lt;span className="px-3 py-1 text-sm rounded-full bg-white/80 dark:bg-gray-800/80 text-gray-700 dark:text-gray-300"&gt;Neural Networks&lt;/span&gt;
                    &lt;span className="px-3 py-1 text-sm rounded-full bg-white/80 dark:bg-gray-800/80 text-gray-700 dark:text-gray-300"&gt;TensorFlow&lt;/span&gt;
                    &lt;span className="px-3 py-1 text-sm rounded-full bg-white/80 dark:bg-gray-800/80 text-gray-700 dark:text-gray-300"&gt;NLP&lt;/span&gt;
                  &lt;/div&gt;
                &lt;/div&gt;
&nbsp;
                &lt;div className="p-6 rounded-xl bg-pink-50 dark:bg-pink-900 backdrop-blur-sm transition-all duration-300 hover:shadow-lg hover:scale-105 hover:bg-pink-100 dark:hover:bg-pink-800"&gt;
                  &lt;div className="flex items-center gap-4 mb-3 transition-transform duration-300 group-hover:scale-110"&gt;
                    &lt;svg
                      stroke="currentColor"
                      fill="currentColor"
                      strokeWidth="0"
                      viewBox="0 0 24 24"
                      className="text-2xl text-pink-500"
                      height="1em"
                      width="1em"
                      xmlns="http://www.w3.org/2000/svg"
                    &gt;
                      &lt;path d="M15.852 8.981h-4.588V0h4.588c2.476 0 4.49 2.014 4.49 4.49s-2.014 4.491-4.49 4.491zM12.735 7.51h3.117c1.665 0 3.019-1.355 3.019-3.019s-1.355-3.019-3.019-3.019h-3.117V7.51zm0 1.471H8.148c-2.476 0-4.49-2.014-4.49-4.49S5.672 0 8.148 0h4.588v8.981zm-4.587-7.51c-1.665 0-3.019 1.355-3.019 3.019s1.354 3.02 3.019 3.02h3.117V1.471H8.148zm4.587 15.019H8.148c-2.476 0-4.49-2.014-4.49-4.49s2.014-4.49 4.49-4.49h4.588v8.98zM8.148 8.981c-1.665 0-3.019 1.355-3.019 3.019s1.355 3.019 3.019 3.019h3.117V8.981H8.148zM8.172 24c-2.489 0-4.515-2.014-4.515-4.49s2.014-4.49 4.49-4.49h4.588v4.441c0 2.503-2.047 4.539-4.563 4.539zm-.024-7.51a3.023 3.023 0 0 0-3.019 3.019c0 1.665 1.365 3.019 3.044 3.019 1.705 0 3.093-1.376 3.093-3.068v-2.97H8.148zm7.704 0h-.098c-2.476 0-4.49-2.014-4.49-4.49s2.014-4.49 4.49-4.49h.098c2.476 0 4.49 2.014 4.49 4.49s-2.014 4.49-4.49 4.49zm-.097-7.509c-1.665 0-3.019 1.355-3.019 3.019s1.355 3.019 3.019 3.019h.098c1.665 0 3.019-1.355 3.019-3.019s-1.355-3.019-3.019-3.019h-.098z"&gt;&lt;/path&gt;
                    &lt;/svg&gt;
                    &lt;h3 className="font-semibold text-lg text-gray-900 dark:text-gray-100"&gt;Design&lt;/h3&gt;
                  &lt;/div&gt;
                  &lt;div className="flex flex-wrap gap-2"&gt;
                    &lt;span className="px-3 py-1 text-sm rounded-full bg-white/80 dark:bg-gray-800/80 text-gray-700 dark:text-gray-300"&gt;UI/UX&lt;/span&gt;
                    &lt;span className="px-3 py-1 text-sm rounded-full bg-white/80 dark:bg-gray-800/80 text-gray-700 dark:text-gray-300"&gt;Graphic Design&lt;/span&gt;
                    &lt;span className="px-3 py-1 text-sm rounded-full bg-white/80 dark:bg-gray-800/80 text-gray-700 dark:text-gray-300"&gt;Figma&lt;/span&gt;
                    &lt;span className="px-3 py-1 text-sm rounded-full bg-white/80 dark:bg-gray-800/80 text-gray-700 dark:text-gray-300"&gt;Prototyping&lt;/span&gt;
                  &lt;/div&gt;
                &lt;/div&gt;
              &lt;/div&gt;
            &lt;/div&gt;
          &lt;/div&gt;
        &lt;/div&gt;
      &lt;/div&gt;
&nbsp;
      {/* Upcoming Events Section */}
      
&nbsp;
&nbsp;
&nbsp;
      {/* Testimonials Section */}
      &lt;section className="py-16"&gt;
  &lt;div className="container mx-auto px-4 text-center"&gt;
    &lt;h2 className="text-3xl font-bold mb-4 text-gray-900 dark:text-gray-100"&gt;
      Want to Learn More?
    &lt;/h2&gt;
    &lt;p className="text-lg text-gray-600 dark:text-gray-300 mb-6"&gt;
      Explore tutorials, coding challenges, and interview prep at GeeksforGeeks.
    &lt;/p&gt;
    &lt;a
      href="https://www.geeksforgeeks.org"
      target="_blank"
      rel="noopener noreferrer"
      className="inline-block bg-gfgsc-green text-white font-semibold px-6 py-3 rounded-lg hover:bg-green-800 transition"
    &gt;
      Visit GeeksforGeeks
    &lt;/a&gt;
  &lt;/div&gt;
&lt;/section&gt;
    &lt;/&gt;
  )
}
&nbsp;
&nbsp;</pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-08-26T09:35:34.902Z
            </div>
        <script src="../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../sorter.js"></script>
        <script src="../block-navigation.js"></script>
    </body>
</html>
    